"use client";
import React from "react";
import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { useParams } from "next/navigation";
import { ArrowLeft, Calendar, User, Clock, Share2 } from "lucide-react";
import { Button } from "@/components/ui/button";

const BlogDetail = () => {
  const params = useParams();
  const blogId = params.id as string;

  // Sample blog data - in a real app, this would be fetched based on the ID
  const blogPost = {
    id: blogId,
    title: "The Art of Chinioti Wood Carving: A Timeless Tradition",
    content: `
      <p>Chinioti wood carving is more than just a craft—it's a living testament to centuries of artistic excellence and cultural heritage. Nestled in the heart of Punjab, Pakistan, the city of Chiniot has been synonymous with exquisite woodwork for over 400 years.</p>
      
      <h2>A Rich Historical Legacy</h2>
      <p>The tradition of wood carving in Chiniot dates back to the Mughal era, when skilled artisans were commissioned to create intricate decorative elements for palaces and mosques. These craftsmen developed unique techniques that have been carefully preserved and passed down through generations.</p>
      
      <p>The art form reached its zenith during the 16th and 17th centuries, when Chinioti craftsmen were renowned throughout the subcontinent for their exceptional skill and attention to detail. Their work adorned the homes of nobility and the walls of sacred spaces alike.</p>
      
      <h2>Traditional Techniques and Tools</h2>
      <p>Chinioti wood carving is characterized by its intricate geometric patterns, floral motifs, and calligraphic designs. Master craftsmen use traditional tools that have remained largely unchanged for centuries:</p>
      
      <ul>
        <li><strong>Chisel and Mallet:</strong> The primary tools for shaping and carving</li>
        <li><strong>Gouges:</strong> For creating curved cuts and hollow areas</li>
        <li><strong>V-tools:</strong> For precise line work and detail</li>
        <li><strong>Rifflers:</strong> For smoothing and refining carved surfaces</li>
      </ul>
      
      <h2>The Wood Selection Process</h2>
      <p>The choice of wood is crucial to the success of any carving project. Chinioti artisans traditionally prefer:</p>
      
      <ul>
        <li><strong>Sheesham (Indian Rosewood):</strong> Known for its durability and beautiful grain</li>
        <li><strong>Teak:</strong> Prized for its resistance to insects and weather</li>
        <li><strong>Walnut:</strong> Favored for its workability and rich color</li>
        <li><strong>Mulberry:</strong> Used for intricate detail work</li>
      </ul>
      
      <h2>Preserving the Tradition</h2>
      <p>Today, we continue this proud tradition by combining time-honored techniques with modern quality standards. Our master craftsmen undergo years of training, learning not just the technical aspects of carving, but also the cultural significance and artistic principles that make Chinioti woodwork truly special.</p>
      
      <p>Each piece we create tells a story—of skilled hands, patient dedication, and a deep respect for the natural beauty of wood. When you choose Chinioti wooden furniture, you're not just buying a piece of furniture; you're investing in a piece of living history.</p>
      
      <h2>The Future of Chinioti Craftsmanship</h2>
      <p>As we look to the future, we remain committed to preserving these traditional techniques while adapting to contemporary needs and tastes. Our goal is to ensure that this magnificent art form continues to thrive for generations to come, bringing the beauty and elegance of Chinioti craftsmanship to homes around the world.</p>
    `,
    image: "/assets/backgrounds/home-bg.png",
    author: "Ahmad Hassan",
    date: "2024-01-15",
    category: "Craftsmanship",
    readTime: "8 min read"
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Back Button */}
      <div className="container mx-auto px-4 py-6">
        <Link href="/blogs">
          <Button variant="outline" className="mb-6">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Blogs
          </Button>
        </Link>
      </div>

      {/* Hero Image */}
      <div className="relative h-64 md:h-96 mb-8">
        <Image
          src={blogPost.image}
          alt={blogPost.title}
          fill
          className="object-cover"
        />
        <div className="absolute inset-0 bg-black/30" />
        <div className="absolute bottom-6 left-6">
          <span className="bg-accent text-white px-3 py-1 rounded-full text-sm font-medium">
            {blogPost.category}
          </span>
        </div>
      </div>

      {/* Article Content */}
      <article className="container mx-auto px-4 max-w-4xl">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Article Header */}
          <header className="mb-8">
            <h1 className="text-3xl md:text-5xl font-bold mb-6 leading-tight">
              {blogPost.title}
            </h1>
            
            <div className="flex flex-wrap items-center gap-6 text-gray-600 mb-6">
              <div className="flex items-center">
                <User className="h-4 w-4 mr-2" />
                <span>{blogPost.author}</span>
              </div>
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2" />
                <span>{new Date(blogPost.date).toLocaleDateString()}</span>
              </div>
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-2" />
                <span>{blogPost.readTime}</span>
              </div>
            </div>

            {/* Share Button */}
            <div className="flex items-center gap-4">
              <Button variant="outline" size="sm">
                <Share2 className="h-4 w-4 mr-2" />
                Share Article
              </Button>
            </div>
          </header>

          {/* Article Body */}
          <div 
            className="prose prose-lg max-w-none"
            dangerouslySetInnerHTML={{ __html: blogPost.content }}
          />

          {/* Article Footer */}
          <footer className="mt-12 pt-8 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Published on {new Date(blogPost.date).toLocaleDateString()}
              </div>
              <Link href="/blogs">
                <Button variant="outline">
                  Read More Articles
                </Button>
              </Link>
            </div>
          </footer>
        </motion.div>
      </article>

      {/* Related Articles Section */}
      <section className="py-16 bg-gray-50 mt-16">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl md:text-3xl font-bold text-center mb-8">
            Related Articles
          </h2>
          <div className="text-center">
            <Link href="/blogs">
              <Button>
                View All Articles
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default BlogDetail;
