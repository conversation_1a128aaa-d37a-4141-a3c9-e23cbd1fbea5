"use client";
import React, { useState } from "react";
import ToolBar from "../components/ToolBar";
import { secondaryFont } from "@/constants/fonts";
import { motion } from "framer-motion";
import Link from "next/link";
import { usePathname } from "next/navigation";
import HamburgerMenu from "../components/ui/HamburgerMenu";
import MobileSidebar from "../components/navigation/MobileSidebar";

const Header = () => {
  const pathname = usePathname();
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  const navItems = [
    { name: "About Us", path: "/about-us" },
    { name: "Contact Us", path: "/contact" },
    // { name: "Video Blogs", path: "/video-blogs" },
    // { name: "Blogs", path: "/blogs" },
  ];

  const mobileNavItems = [
    { name: "Home", path: "/" },
    { name: "About Us", path: "/about-us" },
    { name: "Contact Us", path: "/contact" },
    { name: "Products", path: "/products" },
    { name: "Wishlist", path: "/wishlist" },
    { name: "Profile", path: "/profile" },
  ];

  const toggleMobileSidebar = () => {
    setIsMobileSidebarOpen(!isMobileSidebarOpen);
  };

  const closeMobileSidebar = () => {
    setIsMobileSidebarOpen(false);
  };

  return (
    <>
      <motion.header
        className="bg-white border-b border-gray-200 shadow-sm sticky top-0 z-50"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Main header */}
        <div className="responsive-header container mx-auto">
          <div className="flex items-center justify-between h-14 sm:h-16 md:h-20">
            {/* Mobile hamburger menu */}
            <div className="md:hidden">
              <HamburgerMenu
                isOpen={isMobileSidebarOpen}
                onClick={toggleMobileSidebar}
                size="md"
                className="text-gray-700 hover:text-accent"
                ariaLabel="Toggle navigation menu"
              />
            </div>

            {/* Logo */}
            <Link href="/" className="flex-shrink-0">
              <motion.div
                className={`${secondaryFont} responsive-logo font-bold relative`}
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <span className="text-accent">Chinioti</span>{" "}
                <span className="text-gray-800 hidden xs:inline">Wooden Art</span>
                <span className="text-gray-800 xs:hidden">WA</span>
                <motion.div
                  className="absolute -bottom-1 left-0 h-0.5 bg-accent/30"
                  initial={{ width: "0%" }}
                  whileHover={{ width: "100%" }}
                  transition={{ duration: 0.3 }}
                />
              </motion.div>
            </Link>

          {/* Navigation - Desktop */}
          <nav className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
              <Link
                key={item.path}
                href={item.path}
                className={`relative text-sm font-medium transition-colors duration-200 ${
                  pathname === item.path
                    ? "text-accent"
                    : "text-gray-700 hover:text-accent"
                }`}
              >
                {item.name}
                {pathname === item.path && (
                  <motion.div
                    className="absolute -bottom-1 left-0 right-0 h-0.5 bg-accent"
                    layoutId="activeTab"
                  />
                )}
              </Link>
            ))}
          </nav>

            {/* Right side - Shop Now & Toolbar */}
            <div className="flex items-center space-x-2 sm:space-x-3 md:space-x-4">
              <Link
                href="/products"
                className="hidden md:inline-flex items-center px-3 py-1.5 md:px-4 md:py-2 bg-accent text-white text-xs md:text-sm font-medium rounded-md hover:bg-accent/90 transition-colors duration-200"
              >
                Shop Now
              </Link>
              <ToolBar />
            </div>
          </div>
        </div>
      </motion.header>

      {/* Mobile Sidebar Navigation */}
      <MobileSidebar
        isOpen={isMobileSidebarOpen}
        onClose={closeMobileSidebar}
        navItems={mobileNavItems}
      />
    </>
  );
};

export default Header;
