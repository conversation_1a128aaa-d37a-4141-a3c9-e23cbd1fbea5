"use client";
import React, { useState } from "react";
import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Calendar, User, ArrowRight, Search } from "lucide-react";
import { Input } from "@/components/ui/input";

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  image: string;
  author: string;
  date: string;
  category: string;
  readTime: string;
}

const Blogs = () => {
  const [searchTerm, setSearchTerm] = useState("");

  // Sample blog posts data
  const blogPosts: BlogPost[] = [
    {
      id: "1",
      title: "The Art of Chinioti Wood Carving: A Timeless Tradition",
      excerpt: "Discover the rich history and intricate techniques behind Chinioti wood carving, a craft that has been passed down through generations.",
      image: "/assets/backgrounds/home-bg.png",
      author: "<PERSON>",
      date: "2024-01-15",
      category: "Craftsmanship",
      readTime: "5 min read"
    },
    {
      id: "2",
      title: "Choosing the Right Wood for Your Furniture",
      excerpt: "Learn about different types of wood and how to select the perfect material for your custom furniture pieces.",
      image: "/assets/backgrounds/home-bg.png",
      author: "Fatima Khan",
      date: "2024-01-10",
      category: "Guide",
      readTime: "7 min read"
    },
    {
      id: "3",
      title: "Sustainable Furniture: Our Commitment to the Environment",
      excerpt: "Explore our eco-friendly practices and how we ensure sustainable sourcing of materials for all our furniture.",
      image: "/assets/backgrounds/home-bg.png",
      author: "Ali Raza",
      date: "2024-01-05",
      category: "Sustainability",
      readTime: "4 min read"
    },
    {
      id: "4",
      title: "Caring for Your Wooden Furniture: Maintenance Tips",
      excerpt: "Essential tips and tricks to keep your wooden furniture looking beautiful and lasting for generations.",
      image: "/assets/backgrounds/home-bg.png",
      author: "Sara Ahmed",
      date: "2024-01-01",
      category: "Care",
      readTime: "6 min read"
    },
    {
      id: "5",
      title: "Traditional vs Modern: Blending Styles in Home Decor",
      excerpt: "How to successfully combine traditional Chinioti furniture with modern interior design elements.",
      image: "/assets/backgrounds/home-bg.png",
      author: "Hassan Ali",
      date: "2023-12-28",
      category: "Design",
      readTime: "8 min read"
    },
    {
      id: "6",
      title: "The Journey from Tree to Table: Our Manufacturing Process",
      excerpt: "Take a behind-the-scenes look at how we transform raw wood into beautiful, functional furniture pieces.",
      image: "/assets/backgrounds/home-bg.png",
      author: "Usman Sheikh",
      date: "2023-12-25",
      category: "Process",
      readTime: "10 min read"
    }
  ];

  // Filter posts based on search only
  const filteredPosts = blogPosts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSearch;
  });

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Our <span className="text-accent">Blog</span>
            </h1>
            <p className="text-lg md:text-xl text-gray-600 mb-8">
              Discover insights about woodworking, furniture care, design trends, and the rich heritage of Chinioti craftsmanship.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Search Section */}
      <section className="py-8 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex justify-center">
            {/* Search */}
            <div className="relative w-full max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Search articles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          {filteredPosts.length === 0 ? (
            <div className="text-center py-16">
              <p className="text-gray-600 text-lg">No articles found matching your criteria.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredPosts.map((post, index) => (
                <motion.div
                  key={post.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  <Link href={`/blogs/${post.id}`}>
                    <Card className="h-full hover:shadow-lg transition-shadow duration-300 cursor-pointer group">
                      <div className="relative overflow-hidden">
                        <Image
                          src={post.image}
                          alt={post.title}
                          width={400}
                          height={250}
                          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                        <div className="absolute top-4 left-4">
                          <span className="bg-accent text-white px-3 py-1 rounded-full text-xs font-medium">
                            {post.category}
                          </span>
                        </div>
                      </div>
                      <CardHeader>
                        <h3 className="text-xl font-bold group-hover:text-accent transition-colors duration-200">
                          {post.title}
                        </h3>
                      </CardHeader>
                      <CardContent>
                        <p className="text-gray-600 mb-4 line-clamp-3">{post.excerpt}</p>
                        <div className="flex items-center justify-between text-sm text-gray-500">
                          <div className="flex items-center space-x-4">
                            <div className="flex items-center">
                              <User className="h-4 w-4 mr-1" />
                              {post.author}
                            </div>
                            <div className="flex items-center">
                              <Calendar className="h-4 w-4 mr-1" />
                              {new Date(post.date).toLocaleDateString()}
                            </div>
                          </div>
                          <span>{post.readTime}</span>
                        </div>
                        <div className="mt-4 flex items-center text-accent font-medium group-hover:translate-x-1 transition-transform duration-200">
                          Read More
                          <ArrowRight className="h-4 w-4 ml-1" />
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default Blogs;
